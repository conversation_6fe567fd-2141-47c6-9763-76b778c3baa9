<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/scroll_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_payment_success"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white">

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline_left"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.03892944" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline_right"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.96107054" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline_top"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintGuide_percent="0.03" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline_bottom"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintGuide_percent="0.97194165" />

        <ImageButton
            android:id="@+id/iv_back_arrow"
            android:layout_width="58dp"
            android:layout_height="38dp"
            android:layout_marginTop="8dp"
            android:background="@drawable/button_outline_green"
            android:contentDescription="@string/back_button_onboarding"
            android:scaleType="centerInside"
            android:src="@drawable/back_arrow_black"
            app:layout_constraintBottom_toTopOf="@+id/iv_success_icon"
            app:layout_constraintEnd_toStartOf="@+id/guideline_right"
            app:layout_constraintHorizontal_bias="0.05"
            app:layout_constraintStart_toStartOf="@id/guideline_left"
            app:layout_constraintTop_toTopOf="@id/guideline_top"
            app:layout_constraintVertical_bias="0.0"/>

        <ImageView
            android:id="@+id/iv_success_icon"
            android:layout_width="92dp"
            android:layout_height="92dp"
            android:contentDescription="@string/payment_success_icon"
            android:src="@drawable/ic_check_circle"
            android:layout_marginTop="40sp"
            app:layout_constraintBottom_toTopOf="@+id/tv_payment_success_title"
            app:layout_constraintEnd_toEndOf="@id/guideline_right"
            app:layout_constraintHorizontal_bias="0.498"
            app:layout_constraintStart_toStartOf="@id/guideline_left"
            app:layout_constraintTop_toBottomOf="@id/guideline_top"
            app:layout_constraintVertical_bias="0.0" />

        <TextView
            android:id="@+id/tv_payment_success_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:text="@string/payment_success_title"
            android:textColor="@color/black"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@+id/tv_payment_success_subtitle"
            app:layout_constraintEnd_toEndOf="@id/guideline_right"
            app:layout_constraintHorizontal_bias="0.497"
            app:layout_constraintStart_toStartOf="@id/guideline_left"
            app:layout_constraintTop_toBottomOf="@+id/iv_success_icon" />

        <TextView
            android:id="@+id/tv_payment_success_subtitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/payment_success_subtitle"
            android:textColor="@color/black"
            android:textSize="14sp"
            app:layout_constraintBottom_toTopOf="@+id/card_payment_details"
            app:layout_constraintEnd_toEndOf="@id/guideline_right"
            app:layout_constraintHorizontal_bias="0.494"
            app:layout_constraintStart_toStartOf="@id/guideline_left"
            app:layout_constraintTop_toTopOf="@+id/guideline_top" />

        <androidx.cardview.widget.CardView
            android:id="@+id/card_payment_details"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="15sp"
            android:layout_marginBottom="20sp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:layout_constraintBottom_toTopOf="@+id/ll_action_buttons"
            app:layout_constraintEnd_toEndOf="@id/guideline_right"
            app:layout_constraintStart_toStartOf="@id/guideline_left"
            app:layout_constraintTop_toBottomOf="@+id/tv_payment_success_subtitle">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="16dp"
                android:gravity="center_vertical"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="5sp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingVertical="4dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_payment_status"
                        android:textColor="@color/black"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_payment_status_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/dummy_approved"
                        android:textColor="@color/green"
                        android:textSize="14sp"
                        android:textStyle="bold" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingVertical="4dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_amount"
                        android:textColor="@color/black"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/amount_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/dummy_amount"
                        android:textColor="@color/black"
                        android:textSize="14sp"
                        android:textStyle="bold" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_vat_receipt_screen"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="5sp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingVertical="4dp"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/vat"
                        android:textColor="@color/black"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_vat_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text=""
                        android:textColor="@color/black"
                        android:textSize="14sp"
                        android:textStyle="bold" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_vat_receipt_screen_value"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="5sp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingVertical="4dp"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/vat_amount"
                        android:textColor="@color/black"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_vat_calculated"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text=""
                        android:textColor="@color/black"
                        android:textSize="14sp"
                        android:textStyle="bold" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_tip_receipt_screen"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="5sp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingVertical="4dp"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/tip"
                        android:textColor="@color/black"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_tip_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text=""
                        android:textColor="@color/black"
                        android:textSize="14sp"
                        android:textStyle="bold" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_tip_receipt_screen_value"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="5sp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingVertical="4dp"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/tip_amount"
                        android:textColor="@color/black"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_tip_calculated"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text=""
                        android:textColor="@color/black"
                        android:textSize="14sp"
                        android:textStyle="bold" />
                </LinearLayout>


                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginVertical="4dp"
                    android:background="@color/light_gray" />

                <!-- Ref Number Row -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5sp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingVertical="4dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_receipt_number"
                        android:textColor="@color/black"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/receipt_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/ref_number_value"
                        android:textColor="@color/black"
                        android:textSize="14sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingVertical="4dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_merchant_name"
                        android:textColor="@color/black"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/merchant_name_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/merchant_name_value"
                        android:textColor="@color/black"
                        android:textSize="14sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingVertical="4dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_payment_method"
                        android:textColor="@color/black"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/payment_method_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/payment_method_value"
                        android:textColor="@color/black"
                        android:textSize="14sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingVertical="4dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_date"
                        android:textColor="@color/black"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/date_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/date_value"
                        android:textColor="@color/black"
                        android:textSize="14sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingVertical="4dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_card_detail"
                        android:textColor="@color/black"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/card_detail_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/card_details_value"
                        android:textColor="@color/black"
                        android:textSize="14sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingVertical="4dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_transaction_type"
                        android:textColor="@color/black"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/transaction_type_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/transaction_type_value"
                        android:textColor="@color/black"
                        android:textSize="14sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingVertical="4dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_tvr"
                        android:textColor="@color/black"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tvr_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/tvr_value"
                        android:textColor="@color/black"
                        android:textSize="14sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingVertical="4dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_rrn"
                        android:textColor="@color/black"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/rrn_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/rrn_value"
                        android:textColor="@color/black"
                        android:textSize="14sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingVertical="4dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_auth_code"
                        android:textColor="@color/black"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/auth_code_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/auth_code_value"
                        android:textColor="@color/black"
                        android:textSize="14sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingVertical="4dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_aid"
                        android:textColor="@color/black"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/aid_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/aid_value"
                        android:textColor="@color/black"
                        android:textSize="14sp" />
                </LinearLayout>

            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <LinearLayout
            android:id="@+id/ll_action_buttons"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="40dp"
            android:layout_marginBottom="20sp"
            android:gravity="center"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="@id/guideline_bottom"
            app:layout_constraintStart_toStartOf="@id/guideline_left"
            app:layout_constraintEnd_toEndOf="@id/guideline_right">

            <LinearLayout
                android:id="@+id/ll_print_receipt"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:background="@drawable/button_outline_green"
                android:orientation="horizontal"
                android:gravity="center"
                android:padding="16dp"
                android:clickable="true"
                android:focusable="true">

                <ImageView
                    android:id="@+id/iv_print_icon"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/print"
                    android:contentDescription="Print Receipt"
                    android:layout_marginEnd="8dp"/>

                <TextView
                    android:id="@+id/tv_print_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Print Receipt"
                    android:textAllCaps="false"
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    android:textSize="14sp"/>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_share_via_email"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/button_outline_green"
                android:orientation="horizontal"
                android:gravity="center"
                android:padding="16dp"
                android:clickable="true"
                android:focusable="true">

                <ImageView
                    android:id="@+id/iv_email_icon"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_email"
                    android:contentDescription="@string/btn_share_via_email"
                    android:layout_marginEnd="8dp"/>

                <TextView
                    android:id="@+id/tv_email_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/btn_share_via_email"
                    android:textAllCaps="false"
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    android:textSize="14sp"/>
            </LinearLayout>

        </LinearLayout>



    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>
