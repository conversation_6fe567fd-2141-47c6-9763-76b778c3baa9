package com.hazelpay.merchant.tap2pay.model

import java.text.SimpleDateFormat
import java.util.*

data class ReceiptData(
    // Merchant Information
    val merchantName: String,
    val companyName: String?,
    val merchantAddress: String?,
    val merchantCity: String?,
    val merchantCountry: String?,
    val terminalId: String?,
    
    // Transaction Information
    val receiptNumber: String,
    val amount: String,
    val currency: String,
    val dateTime: String,
    val paymentStatus: String = "APPROVED",
    
    // Payment Method Information
    val paymentMethod: String,
    val cardMask: String,
    val transactionType: String,
    
    // Technical Information
    val tvr: String?,
    val rrn: String,
    val authCode: String,
    val aid: String,
    
    // Optional Information
    val vatAmount: String? = null,
    val vatPercentage: String? = null,
    val tipAmount: String? = null,
    val tipPercentage: String? = null
) {
    companion object {
        fun fromPaymentReceipt(
            paymentReceipt: PaymentReceipt,
            userData: UserData?,
            vatAmount: String? = null,
            vatPercentage: String? = null,
            tipAmount: String? = null,
            tipPercentage: String? = null
        ): ReceiptData {
            return ReceiptData(
                merchantName = userData?.merchantName ?: paymentReceipt.pmt_dest,
                companyName = userData?.company,
                merchantAddress = userData?.address,
                merchantCity = userData?.city,
                merchantCountry = userData?.country,
                terminalId = paymentReceipt.terminal,
                receiptNumber = paymentReceipt.trxid,
                amount = paymentReceipt.amt,
                currency = paymentReceipt.cur_code,
                dateTime = paymentReceipt.date_time,
                paymentStatus = if (paymentReceipt.status.equals("success", ignoreCase = true)) "APPROVED" else paymentReceipt.status.uppercase(),
                paymentMethod = paymentReceipt.applbl,
                cardMask = paymentReceipt.card_mask,
                transactionType = paymentReceipt.pmt_name,
                tvr = paymentReceipt.tvr,
                rrn = paymentReceipt.rrn,
                authCode = paymentReceipt.auth_code,
                aid = paymentReceipt.aid,
                vatAmount = vatAmount,
                vatPercentage = vatPercentage,
                tipAmount = tipAmount,
                tipPercentage = tipPercentage
            )
        }
    }
    
    fun getFormattedDateTime(): String {
        return try {
            val inputFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
            val outputFormat = SimpleDateFormat("dd/MM/yyyy HH:mm:ss", Locale.getDefault())
            val date = inputFormat.parse(dateTime)
            outputFormat.format(date ?: Date())
        } catch (e: Exception) {
            dateTime
        }
    }
    
    fun getFormattedAmount(): String {
        return "$currency $amount"
    }
    
    fun getFormattedVatAmount(): String? {
        return if (vatAmount != null) "$currency $vatAmount" else null
    }
    
    fun getFormattedTipAmount(): String? {
        return if (tipAmount != null) "$currency $tipAmount" else null
    }
}
