package com.hazelpay.merchant.tap2pay.utils

import android.content.Context
import android.util.Log
import android.widget.Toast
import com.hazelpay.merchant.tap2pay.model.ReceiptData
import java.text.SimpleDateFormat
import java.util.*

/**
 * Utility class for Landi POS terminal thermal printer
 * This class handles direct thermal printing for Landi devices
 */
object LandiPrinterUtils {

    private const val TAG = "LandiPrinterUtils"

    /**
     * Print receipt using Landi thermal printer
     */
    fun printReceipt(context: Context, receiptData: ReceiptData) {
        try {
            Log.d(TAG, "Starting receipt printing process...")

            // Debug: Log available printer methods
            debugPrinterAvailability()

            // Try Android native printing first (might work with some Landi devices)
            if (tryAndroidNativePrinting(context, receiptData)) {
                Toast.makeText(context, "Printing receipt...", Toast.LENGTH_SHORT).show()
                return
            }

            // Generate receipt content for thermal printing
            val receiptContent = generateReceiptContent(receiptData)
            Log.d(TAG, "Generated receipt content (${receiptContent.length} chars)")

            // Try thermal printer methods
            if (printThermalReceipt(receiptContent)) {
                Toast.makeText(context, "Printing receipt...", Toast.LENGTH_SHORT).show()
            } else {
                // Show user a dialog with receipt content as fallback
                showReceiptDialog(context, receiptContent)
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error printing receipt", e)
            Toast.makeText(context, "Print error: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    /**
     * Try Android native printing (might work with some Landi devices)
     */
    private fun tryAndroidNativePrinting(context: Context, receiptData: ReceiptData): Boolean {
        return try {
            Log.d(TAG, "Trying Android native printing...")

            // Import the original ReceiptGenerator for fallback
            val originalReceiptGenerator = Class.forName("com.hazelpay.merchant.tap2pay.utils.ReceiptGenerator")
            val generateMethod = originalReceiptGenerator.getMethod("generateReceipt", Context::class.java, receiptData::class.java)
            generateMethod.invoke(null, context, receiptData)

            Log.d(TAG, "Android native printing initiated")
            true
        } catch (e: Exception) {
            Log.w(TAG, "Android native printing failed: ${e.message}")
            false
        }
    }

    /**
     * Show receipt content in a dialog as fallback
     */
    private fun showReceiptDialog(context: Context, content: String) {
        try {
            // Create a simple text receipt without ESC/POS commands
            val cleanContent = content
                .replace("\u001B@", "")  // Remove INIT
                .replace("\u001Ba1", "")  // Remove CENTER
                .replace("\u001Ba0", "")  // Remove LEFT
                .replace("\u001BE1", "")  // Remove BOLD_ON
                .replace("\u001BE0", "")  // Remove BOLD_OFF
                .replace("\u001B!1", "")  // Remove LARGE_ON
                .replace("\u001B!0", "")  // Remove LARGE_OFF
                .replace("\u001B!16", "") // Remove DOUBLE_HEIGHT
                .replace("\u001Bd3", "")  // Remove CUT
                .replace("\u001Bi", "")   // Remove FULL_CUT
                .replace("\u001B-1", "")  // Remove UNDERLINE_ON
                .replace("\u001B-0", "")  // Remove UNDERLINE_OFF

            Toast.makeText(context, "Printer not available. Receipt content logged.", Toast.LENGTH_LONG).show()
            Log.i(TAG, "RECEIPT CONTENT:\n$cleanContent")

        } catch (e: Exception) {
            Log.e(TAG, "Error showing receipt dialog", e)
        }
    }

    /**
     * Debug printer availability and methods
     */
    private fun debugPrinterAvailability() {
        Log.d(TAG, "=== Printer Debug Information ===")

        // Check device properties
        debugDeviceProperties()

        // Check available printer devices
        debugPrinterDevices()

        // Check Landi classes
        val landiClasses = listOf(
            "com.landi.printer.PrinterManager",
            "com.landicorp.printer.PrinterManager",
            "com.landi.hardware.printer.PrinterManager",
            "com.landi.printer.service.PrinterService",
            "com.landicorp.printer.service.PrinterService"
        )

        for (className in landiClasses) {
            try {
                val clazz = Class.forName(className)
                Log.d(TAG, "Found Landi class: $className")

                // Log available methods
                val methods = clazz.methods.filter {
                    it.name.contains("print", ignoreCase = true) ||
                    it.name.contains("getInstance", ignoreCase = true)
                }
                Log.d(TAG, "  Available methods: ${methods.map { it.name }}")
            } catch (e: ClassNotFoundException) {
                Log.d(TAG, "Landi class not found: $className")
            }
        }

        Log.d(TAG, "=== End Printer Debug ===")
    }

    /**
     * Debug device properties to identify Landi model
     */
    private fun debugDeviceProperties() {
        try {
            val manufacturer = android.os.Build.MANUFACTURER
            val model = android.os.Build.MODEL
            val device = android.os.Build.DEVICE
            val product = android.os.Build.PRODUCT

            Log.d(TAG, "Device Info:")
            Log.d(TAG, "  Manufacturer: $manufacturer")
            Log.d(TAG, "  Model: $model")
            Log.d(TAG, "  Device: $device")
            Log.d(TAG, "  Product: $product")

            // Check for Landi-specific properties
            val landiProperties = listOf(
                "ro.landi.model",
                "ro.landicorp.model",
                "ro.product.landi",
                "persist.landi.printer"
            )

            for (prop in landiProperties) {
                try {
                    val value = System.getProperty(prop)
                    if (value != null) {
                        Log.d(TAG, "  $prop: $value")
                    }
                } catch (e: Exception) {
                    // Property not found
                }
            }
        } catch (e: Exception) {
            Log.w(TAG, "Error getting device properties", e)
        }
    }

    /**
     * Debug available printer devices
     */
    private fun debugPrinterDevices() {
        try {
            val printerPaths = listOf(
                "/dev/ttyS0", "/dev/ttyS1", "/dev/ttyS2", "/dev/ttyS3",
                "/dev/ttyUSB0", "/dev/ttyUSB1",
                "/dev/usb/lp0", "/dev/usb/lp1",
                "/sys/class/thermal_printer",
                "/dev/thermal_printer",
                "/dev/landi_printer"
            )

            Log.d(TAG, "Checking printer device paths:")
            for (path in printerPaths) {
                try {
                    val file = java.io.File(path)
                    if (file.exists()) {
                        Log.d(TAG, "  Found device: $path (readable: ${file.canRead()}, writable: ${file.canWrite()})")
                    }
                } catch (e: Exception) {
                    // Path not accessible
                }
            }
        } catch (e: Exception) {
            Log.w(TAG, "Error checking printer devices", e)
        }
    }

    /**
     * Check if thermal printer is available
     */
    private fun isPrinterAvailable(): Boolean {
        return try {
            // Try to access Landi printer service
            // This is a placeholder - you'll need to implement based on Landi SDK
            val printerClass = Class.forName("com.landi.printer.PrinterManager")
            true
        } catch (e: ClassNotFoundException) {
            Log.w(TAG, "Landi printer class not found, trying alternative methods")
            // Try alternative printer access methods
            checkAlternativePrinterMethods()
        } catch (e: Exception) {
            Log.e(TAG, "Error checking printer availability", e)
            false
        }
    }

    /**
     * Check for alternative printer access methods
     */
    private fun checkAlternativePrinterMethods(): Boolean {
        return try {
            // Try common Landi printer service names
            val serviceNames = listOf(
                "com.landi.printer.service.PrinterService",
                "com.landicorp.printer.PrinterManager",
                "android.hardware.thermal.ThermalPrinter"
            )

            for (serviceName in serviceNames) {
                try {
                    Class.forName(serviceName)
                    Log.d(TAG, "Found printer service: $serviceName")
                    return true
                } catch (e: ClassNotFoundException) {
                    continue
                }
            }
            false
        } catch (e: Exception) {
            false
        }
    }

    /**
     * Generate thermal printer compatible receipt content
     */
    private fun generateReceiptContent(receiptData: ReceiptData): String {
        val content = StringBuilder()

        // ESC/POS commands for thermal printer formatting
        val ESC = "\u001B"
        val INIT = "$ESC@"                    // Initialize printer
        val CENTER = "$ESC" + "a1"            // Center alignment
        val LEFT = "$ESC" + "a0"              // Left alignment
        val BOLD_ON = "$ESC" + "E1"           // Bold on
        val BOLD_OFF = "$ESC" + "E0"          // Bold off
        val LARGE_ON = "$ESC" + "!1"          // Large text on
        val LARGE_OFF = "$ESC" + "!0"         // Large text off
        val DOUBLE_HEIGHT = "$ESC" + "!16"    // Double height text
        val NORMAL_SIZE = "$ESC" + "!0"       // Normal size text
        val CUT = "$ESC" + "d3"               // Cut paper
        val FULL_CUT = "$ESC" + "i"           // Full cut
        val LINE_FEED = "\n"
        val FORM_FEED = "\u000c"                  // Form feed

        // Additional formatting
        val UNDERLINE_ON = "$ESC" + "-1"      // Underline on
        val UNDERLINE_OFF = "$ESC" + "-0"     // Underline off

        content.append(INIT)

        // Header - Company Information
        content.append(CENTER)
        content.append(BOLD_ON)
        content.append(LARGE_ON)
        if (!receiptData.companyName.isNullOrEmpty()) {
            content.append(receiptData.companyName.uppercase())
            content.append(LINE_FEED)
        }
        content.append(LARGE_OFF)
        content.append(receiptData.merchantName)
        content.append(LINE_FEED)
        content.append(BOLD_OFF)

        if (!receiptData.merchantAddress.isNullOrEmpty()) {
            val address = buildString {
                append(receiptData.merchantAddress)
                if (!receiptData.merchantCity.isNullOrEmpty()) {
                    append(", ${receiptData.merchantCity}")
                }
                if (!receiptData.merchantCountry.isNullOrEmpty()) {
                    append(", ${receiptData.merchantCountry}")
                }
            }
            content.append(address)
            content.append(LINE_FEED)
        }

        content.append(LINE_FEED)

        // Receipt details
        content.append(LEFT)
        content.append("Receipt #: ${receiptData.receiptNumber}")
        content.append(LINE_FEED)
        content.append("Date/Time: ${receiptData.getFormattedDateTime()}")
        content.append(LINE_FEED)

        // Separator
        content.append("--------------------------------")
        content.append(LINE_FEED)

        // VAT and Tip (if present)
        if (!receiptData.vatAmount.isNullOrEmpty() &&
            receiptData.vatAmount != "0" &&
            receiptData.vatAmount != "0.00") {
            content.append("VAT (${receiptData.vatPercentage}%): ${receiptData.getFormattedVatAmount()}")
            content.append(LINE_FEED)
        }

        if (!receiptData.tipAmount.isNullOrEmpty() &&
            receiptData.tipAmount != "0" &&
            receiptData.tipAmount != "0.00") {
            content.append("Tip (${receiptData.tipPercentage}%): ${receiptData.getFormattedTipAmount()}")
            content.append(LINE_FEED)
        }

        // Subtotal
        val baseAmount = receiptData.amount.toDoubleOrNull() ?: 0.0
        val vatAmount = receiptData.vatAmount?.toDoubleOrNull() ?: 0.0
        val tipAmount = receiptData.tipAmount?.toDoubleOrNull() ?: 0.0
        val subtotal = baseAmount - vatAmount - tipAmount
        content.append("Subtotal: ${receiptData.currency} ${String.format("%.2f", subtotal)}")
        content.append(LINE_FEED)

        // Separator
        content.append("--------------------------------")
        content.append(LINE_FEED)

        // Total
        content.append(BOLD_ON)
        content.append(LARGE_ON)
        content.append("TOTAL: ${receiptData.getFormattedAmount()}")
        content.append(LINE_FEED)
        content.append(LARGE_OFF)
        content.append(BOLD_OFF)

        // Status
        content.append(CENTER)
        content.append(BOLD_ON)
        content.append("*** ${receiptData.paymentStatus} ***")
        content.append(LINE_FEED)
        content.append(BOLD_OFF)

        // Separator
        content.append("................................")
        content.append(LINE_FEED)

        // Transaction details
        content.append(LEFT)
        if (!receiptData.terminalId.isNullOrEmpty()) {
            content.append("TID: ${receiptData.terminalId}")
            content.append(LINE_FEED)
        }
        content.append("MID: ${receiptData.merchantName.take(15)}")
        content.append(LINE_FEED)
        content.append("Payment Method: ${receiptData.paymentMethod}")
        content.append(LINE_FEED)
        content.append("Card Number: ${receiptData.cardMask}")
        content.append(LINE_FEED)
        content.append("Transaction Type: ${receiptData.transactionType}")
        content.append(LINE_FEED)
        content.append("RRN: ${receiptData.rrn}")
        content.append(LINE_FEED)
        content.append("Auth Code: ${receiptData.authCode}")
        content.append(LINE_FEED)
        content.append("AID: ${receiptData.aid}")
        content.append(LINE_FEED)

        if (!receiptData.tvr.isNullOrEmpty()) {
            content.append("TVR: ${receiptData.tvr}")
            content.append(LINE_FEED)
        }

        content.append(LINE_FEED)

        // Footer
        content.append(CENTER)
        content.append("Thank you for your business!")
        content.append(LINE_FEED)
        content.append("Keep this receipt for your records")
        content.append(LINE_FEED)
        content.append(LINE_FEED)
        content.append("Made by HazelsOne a product of EftaaPay")
        content.append(LINE_FEED)
        content.append(LINE_FEED)

        // Cut paper
        content.append(CUT)

        return content.toString()
    }

    /**
     * Send content to thermal printer
     */
    private fun printThermalReceipt(content: String) {
        try {
            // Method 1: Try Landi specific printer API
            if (tryLandiPrinterAPI(content)) {
                return
            }

            // Method 2: Try generic thermal printer interface
            if (tryGenericThermalPrinter(content)) {
                return
            }

            // Method 3: Try Android Intent-based printing
            if (tryIntentBasedPrinting(content)) {
                return
            }

            // Method 4: Try system print service
            trySystemPrintService(content)

        } catch (e: Exception) {
            Log.e(TAG, "Error in thermal printing", e)
            throw e
        }
    }

    /**
     * Try Landi specific printer API
     */
    private fun tryLandiPrinterAPI(content: String): Boolean {
        return try {
            // Method 1: Try Landi PrinterManager
            if (tryLandiPrinterManager(content)) return true

            // Method 2: Try Landi PrinterService
            if (tryLandiPrinterService(content)) return true

            // Method 3: Try system printer interface
            if (trySystemPrinterInterface(content)) return true

            Log.d(TAG, "All Landi printer methods failed")
            false
        } catch (e: Exception) {
            Log.w(TAG, "Landi printer API not available", e)
            false
        }
    }

    /**
     * Try Landi PrinterManager approach
     */
    private fun tryLandiPrinterManager(content: String): Boolean {
        return try {
            // Common Landi printer manager class names
            val printerManagerClasses = listOf(
                "com.landi.printer.PrinterManager",
                "com.landicorp.printer.PrinterManager",
                "com.landi.hardware.printer.PrinterManager"
            )

            for (className in printerManagerClasses) {
                try {
                    val printerClass = Class.forName(className)
                    val getInstance = printerClass.getMethod("getInstance")
                    val printerManager = getInstance.invoke(null)

                    // Try to print
                    val printTextMethod = printerClass.getMethod("printText", String::class.java)
                    printTextMethod.invoke(printerManager, content)

                    // Try to cut paper
                    try {
                        val cutPaperMethod = printerClass.getMethod("cutPaper")
                        cutPaperMethod.invoke(printerManager)
                    } catch (e: Exception) {
                        Log.w(TAG, "Cut paper method not available")
                    }

                    Log.d(TAG, "Successfully printed using $className")
                    return true
                } catch (e: ClassNotFoundException) {
                    continue
                } catch (e: Exception) {
                    Log.w(TAG, "Error with $className: ${e.message}")
                    continue
                }
            }
            false
        } catch (e: Exception) {
            Log.w(TAG, "PrinterManager approach failed", e)
            false
        }
    }

    /**
     * Try Landi PrinterService approach
     */
    private fun tryLandiPrinterService(content: String): Boolean {
        return try {
            // Try to access printer through system service
            val serviceClasses = listOf(
                "com.landi.printer.service.PrinterService",
                "com.landicorp.printer.service.PrinterService"
            )

            for (className in serviceClasses) {
                try {
                    val serviceClass = Class.forName(className)
                    val constructor = serviceClass.getConstructor()
                    val service = constructor.newInstance()

                    val printMethod = serviceClass.getMethod("print", String::class.java)
                    printMethod.invoke(service, content)

                    Log.d(TAG, "Successfully printed using $className")
                    return true
                } catch (e: ClassNotFoundException) {
                    continue
                } catch (e: Exception) {
                    Log.w(TAG, "Error with $className: ${e.message}")
                    continue
                }
            }
            false
        } catch (e: Exception) {
            Log.w(TAG, "PrinterService approach failed", e)
            false
        }
    }

    /**
     * Try system printer interface with proper serial communication
     */
    private fun trySystemPrinterInterface(content: String): Boolean {
        return try {
            // Try different approaches for Landi thermal printer
            if (trySerialPrinterWithStty(content)) return true
            if (tryDirectSerialWrite(content)) return true
            if (trySystemPrintCommand(content)) return true

            false
        } catch (e: Exception) {
            Log.w(TAG, "System printer interface failed", e)
            false
        }
    }

    /**
     * Try printing using stty to configure serial port properly
     */
    private fun trySerialPrinterWithStty(content: String): Boolean {
        return try {
            val printerPaths = listOf("/dev/ttyS0", "/dev/ttyS1", "/dev/ttyUSB0")

            for (path in printerPaths) {
                try {
                    Log.d(TAG, "Trying serial printer with stty: $path")

                    // Configure serial port for thermal printer (9600 baud, 8N1)
                    val configProcess = Runtime.getRuntime().exec("stty -F $path 9600 cs8 -cstopb -parenb raw")
                    configProcess.waitFor()

                    // Write content to printer
                    val printProcess = Runtime.getRuntime().exec(arrayOf("sh", "-c", "echo -e '$content' > $path"))
                    val exitCode = printProcess.waitFor()

                    if (exitCode == 0) {
                        Log.d(TAG, "Successfully printed using stty method: $path")
                        return true
                    }
                } catch (e: Exception) {
                    Log.w(TAG, "Failed stty method for $path: ${e.message}")
                    continue
                }
            }
            false
        } catch (e: Exception) {
            Log.w(TAG, "Serial printer with stty failed", e)
            false
        }
    }

    /**
     * Try direct serial write using cat command
     */
    private fun tryDirectSerialWrite(content: String): Boolean {
        return try {
            val printerPaths = listOf("/dev/ttyS0", "/dev/ttyS1", "/dev/ttyUSB0")

            for (path in printerPaths) {
                try {
                    Log.d(TAG, "Trying direct serial write: $path")

                    // Use cat to write binary data properly
                    val process = Runtime.getRuntime().exec(arrayOf("sh", "-c", "printf '$content' | cat > $path"))
                    val exitCode = process.waitFor()

                    if (exitCode == 0) {
                        Log.d(TAG, "Successfully printed using direct write: $path")
                        return true
                    }
                } catch (e: Exception) {
                    Log.w(TAG, "Failed direct write for $path: ${e.message}")
                    continue
                }
            }
            false
        } catch (e: Exception) {
            Log.w(TAG, "Direct serial write failed", e)
            false
        }
    }

    /**
     * Try system print command (lp, lpr)
     */
    private fun trySystemPrintCommand(content: String): Boolean {
        return try {
            val printCommands = listOf("lp", "lpr")

            for (command in printCommands) {
                try {
                    Log.d(TAG, "Trying system print command: $command")

                    val process = Runtime.getRuntime().exec(arrayOf("sh", "-c", "echo '$content' | $command"))
                    val exitCode = process.waitFor()

                    if (exitCode == 0) {
                        Log.d(TAG, "Successfully printed using $command")
                        return true
                    }
                } catch (e: Exception) {
                    Log.w(TAG, "Failed $command: ${e.message}")
                    continue
                }
            }
            false
        } catch (e: Exception) {
            Log.w(TAG, "System print command failed", e)
            false
        }
    }

    /**
     * Try generic thermal printer interface
     */
    private fun tryGenericThermalPrinter(content: String): Boolean {
        return try {
            // Try to access thermal printer through system services
            val runtime = Runtime.getRuntime()
            val process = runtime.exec("echo '$content' > /dev/usb/lp0")
            process.waitFor()
            true
        } catch (e: Exception) {
            Log.w(TAG, "Generic thermal printer not available", e)
            false
        }
    }

    /**
     * Try Android Intent-based printing
     */
    private fun tryIntentBasedPrinting(content: String): Boolean {
        return try {
            Log.d(TAG, "Trying Intent-based printing")

            // Try to find printer apps that can handle text printing
            val printIntents = listOf(
                "android.intent.action.PRINT",
                "com.landi.printer.action.PRINT",
                "com.landicorp.printer.action.PRINT"
            )

            for (action in printIntents) {
                try {
                    // This would need to be called from an Activity context
                    // For now, just log that we would try this method
                    Log.d(TAG, "Would try Intent action: $action")
                } catch (e: Exception) {
                    Log.w(TAG, "Intent action $action failed: ${e.message}")
                    continue
                }
            }

            false // Return false for now since we can't actually launch intents from here
        } catch (e: Exception) {
            Log.w(TAG, "Intent-based printing failed", e)
            false
        }
    }

    /**
     * Try system print service as fallback
     */
    private fun trySystemPrintService(content: String): Boolean {
        return try {
            // Try CUPS printing if available
            val cupsCommands = listOf(
                "lp -d thermal_printer",
                "lp -d printer",
                "lpr -P thermal_printer",
                "lpr -P printer"
            )

            for (command in cupsCommands) {
                try {
                    Log.d(TAG, "Trying CUPS command: $command")
                    val process = Runtime.getRuntime().exec(arrayOf("sh", "-c", "echo '$content' | $command"))
                    val exitCode = process.waitFor()

                    if (exitCode == 0) {
                        Log.d(TAG, "Successfully printed using CUPS: $command")
                        return true
                    }
                } catch (e: Exception) {
                    Log.w(TAG, "CUPS command $command failed: ${e.message}")
                    continue
                }
            }

            Log.d(TAG, "All printing methods failed")
            false
        } catch (e: Exception) {
            Log.w(TAG, "System print service not available", e)
            false
        }
    }
}
