package com.hazelpay.merchant.tap2pay.utils

import android.content.Context
import android.graphics.*
import android.graphics.pdf.PdfDocument
import android.print.PrintAttributes
import android.print.PrintDocumentAdapter
import android.print.PrintDocumentInfo
import android.print.PrintManager
import android.util.Log
import android.widget.Toast
import com.hazelpay.merchant.tap2pay.model.ReceiptData
import java.io.FileOutputStream

class ReceiptGenerator {

    companion object {
        private const val TAG = "ReceiptGenerator"
        private const val PAGE_WIDTH = 612 // A4 width in points (8.5 inches * 72 DPI)
        private const val RECEIPT_WIDTH = 400 // Reduced receipt width for better centering
        private const val MARGIN = 32
        private const val LINE_HEIGHT = 36
        private const val SMALL_LINE_HEIGHT = 30
        private const val HEADER_SIZE = 20f
        private const val NORMAL_SIZE = 16f
        private const val SMALL_SIZE = 14f
        private const val LARGE_SIZE = 24f

        // Calculate centering offset - center the receipt on the page
        private val RECEIPT_X_OFFSET = (PAGE_WIDTH - RECEIPT_WIDTH) / 2f

        fun generateReceipt(context: Context, receiptData: ReceiptData) {
            try {
                // Try Landi thermal printer first
                LandiPrinterUtils.printReceipt(context, receiptData)

            } catch (e: Exception) {
                Log.e(TAG, "Error generating receipt", e)
                Toast.makeText(context, "Error generating receipt: ${e.message}", Toast.LENGTH_LONG).show()
            }
        }

        private fun createReceiptPdf(receiptData: ReceiptData): PdfDocument {
            val pdfDocument = PdfDocument()

            // Calculate page height based on content, with minimum height for proper centering
            val contentHeight = calculatePageHeight(receiptData)
            val pageHeight = maxOf(contentHeight, 792) // Ensure minimum A4 height (11 inches * 72 DPI)

            val pageInfo = PdfDocument.PageInfo.Builder(PAGE_WIDTH, pageHeight, 1).create()
            val page = pdfDocument.startPage(pageInfo)
            val canvas = page.canvas

            drawReceiptContent(canvas, receiptData)

            pdfDocument.finishPage(page)
            return pdfDocument
        }

        private fun calculatePageHeight(receiptData: ReceiptData): Int {
            var height = MARGIN * 2 // Top and bottom margins
            height += LINE_HEIGHT * 3 // Merchant info (3 lines max)
            height += SMALL_LINE_HEIGHT * 2 // Receipt # and Date/Time
            height += LINE_HEIGHT // Separator

            // VAT if present and not zero
            if (!receiptData.vatAmount.isNullOrEmpty() &&
                receiptData.vatAmount != "0" &&
                receiptData.vatAmount != "0.00" &&
                !receiptData.vatPercentage.isNullOrEmpty()) {
                height += SMALL_LINE_HEIGHT
            }

            // Tip if present and not zero
            if (!receiptData.tipAmount.isNullOrEmpty() &&
                receiptData.tipAmount != "0" &&
                receiptData.tipAmount != "0.00" &&
                !receiptData.tipPercentage.isNullOrEmpty()) {
                height += SMALL_LINE_HEIGHT
            }

            // Subtotal (always present)
            height += SMALL_LINE_HEIGHT

            height += LINE_HEIGHT // Separator
            height += LINE_HEIGHT // Total amount (larger)
            height += LINE_HEIGHT // Status
            height += LINE_HEIGHT // Separator
            height += SMALL_LINE_HEIGHT * 8 // Transaction details (TID, MID, card info, etc.)
            height += LINE_HEIGHT * 4 // Footer (including HazelsOne branding)

            return height
        }

        private fun drawReceiptContent(canvas: Canvas, receiptData: ReceiptData) {
            val paint = Paint().apply {
                isAntiAlias = true
                color = Color.BLACK
            }

            // Draw a light border around the receipt area for debugging (optional)
            val borderPaint = Paint().apply {
                color = Color.LTGRAY
                strokeWidth = 1f
                style = Paint.Style.STROKE
            }
            canvas.drawRect(
                RECEIPT_X_OFFSET,
                50f,
                RECEIPT_X_OFFSET + RECEIPT_WIDTH,
                calculatePageHeight(receiptData).toFloat() + 50f,
                borderPaint
            )

            // Start with more top margin for better vertical centering
            var yPosition = MARGIN.toFloat() + 50f

            // 1. Header - Merchant Information
            yPosition = drawMerchantHeader(canvas, paint, receiptData, yPosition)

            // 2. Receipt Number and Date/Time
            yPosition = drawReceiptAndDateTime(canvas, paint, receiptData, yPosition)

            // Separator line
            yPosition = drawSeparatorLine(canvas, paint, yPosition)

            // 3. VAT and Tip (if present), then Subtotal
            yPosition = drawVatTipSubtotal(canvas, paint, receiptData, yPosition)

            // Separator line
            yPosition = drawSeparatorLine(canvas, paint, yPosition)

            // 4. Total Amount and Status
            yPosition = drawTotalAndStatus(canvas, paint, receiptData, yPosition)

            // Separator line (dotted for better visual separation)
            yPosition = drawDottedSeparatorLine(canvas, paint, yPosition)

            // 5. Transaction Details (Card Info, TID, MID, etc.)
            yPosition = drawTransactionDetails(canvas, paint, receiptData, yPosition)

            // Footer
            drawFooter(canvas, paint, yPosition)
        }

        private fun drawMerchantHeader(canvas: Canvas, paint: Paint, receiptData: ReceiptData, startY: Float): Float {
            var yPos = startY
            val centerX = RECEIPT_X_OFFSET + (RECEIPT_WIDTH / 2f)

            // Company Name (Large, Bold, Centered)
            if (!receiptData.companyName.isNullOrEmpty()) {
                paint.textSize = LARGE_SIZE
                paint.typeface = Typeface.DEFAULT_BOLD
                paint.textAlign = Paint.Align.CENTER
                canvas.drawText(receiptData.companyName.uppercase(), centerX, yPos, paint)
                yPos += LINE_HEIGHT
            }

            // Company ID / Merchant Name (if different from company name)
            paint.textSize = NORMAL_SIZE
            paint.typeface = Typeface.DEFAULT
            paint.textAlign = Paint.Align.CENTER
            canvas.drawText(receiptData.merchantName, centerX, yPos, paint)
            yPos += LINE_HEIGHT

            // Company Address (if available)
            if (!receiptData.merchantAddress.isNullOrEmpty()) {
                paint.textSize = SMALL_SIZE
                paint.typeface = Typeface.DEFAULT
                val addressLine = buildString {
                    append(receiptData.merchantAddress)
                    if (!receiptData.merchantCity.isNullOrEmpty()) {
                        append(", ${receiptData.merchantCity}")
                    }
                    if (!receiptData.merchantCountry.isNullOrEmpty()) {
                        append(", ${receiptData.merchantCountry}")
                    }
                }
                canvas.drawText(addressLine, centerX, yPos, paint)
                yPos += SMALL_LINE_HEIGHT
            }

            return yPos + LINE_HEIGHT / 2
        }

        private fun drawReceiptAndDateTime(canvas: Canvas, paint: Paint, receiptData: ReceiptData, startY: Float): Float {
            var yPos = startY
            val leftX = RECEIPT_X_OFFSET + MARGIN
            val rightX = RECEIPT_X_OFFSET + RECEIPT_WIDTH - MARGIN

            paint.textSize = SMALL_SIZE
            paint.typeface = Typeface.DEFAULT

            // Receipt Number
            paint.textAlign = Paint.Align.LEFT
            canvas.drawText("Receipt #:", leftX, yPos, paint)
            paint.textAlign = Paint.Align.RIGHT
            canvas.drawText(receiptData.receiptNumber, rightX, yPos, paint)
            yPos += SMALL_LINE_HEIGHT

            // Date/Time
            paint.textAlign = Paint.Align.LEFT
            canvas.drawText("Date/Time:", leftX, yPos, paint)
            paint.textAlign = Paint.Align.RIGHT
            canvas.drawText(receiptData.getFormattedDateTime(), rightX, yPos, paint)
            yPos += SMALL_LINE_HEIGHT

            return yPos + LINE_HEIGHT / 2
        }

        private fun drawSeparatorLine(canvas: Canvas, paint: Paint, yPos: Float): Float {
            val linePaint = Paint().apply {
                color = Color.BLACK
                strokeWidth = 2f
            }
            val leftX = RECEIPT_X_OFFSET + MARGIN
            val rightX = RECEIPT_X_OFFSET + RECEIPT_WIDTH - MARGIN
            canvas.drawLine(leftX, yPos, rightX, yPos, linePaint)
            return yPos + LINE_HEIGHT / 2
        }

        private fun drawDottedSeparatorLine(canvas: Canvas, paint: Paint, yPos: Float): Float {
            val linePaint = Paint().apply {
                color = Color.GRAY
                strokeWidth = 1f
            }

            // Draw dotted line
            val dotSpacing = 8f
            var x = RECEIPT_X_OFFSET + MARGIN
            while (x < RECEIPT_X_OFFSET + RECEIPT_WIDTH - MARGIN) {
                canvas.drawCircle(x, yPos, 1f, linePaint)
                x += dotSpacing
            }

            return yPos + LINE_HEIGHT / 2
        }

        private fun drawTotalAndStatus(canvas: Canvas, paint: Paint, receiptData: ReceiptData, startY: Float): Float {
            var yPos = startY
            val leftX = RECEIPT_X_OFFSET + MARGIN
            val rightX = RECEIPT_X_OFFSET + RECEIPT_WIDTH - MARGIN
            val centerX = RECEIPT_X_OFFSET + (RECEIPT_WIDTH / 2f)

            // Add some spacing before total
            yPos += SMALL_LINE_HEIGHT / 2

            // Total Amount (Extra Large, Bold)
            paint.textSize = LARGE_SIZE + 4f
            paint.typeface = Typeface.DEFAULT_BOLD
            paint.textAlign = Paint.Align.LEFT
            canvas.drawText("TOTAL:", leftX, yPos, paint)
            paint.textAlign = Paint.Align.RIGHT
            canvas.drawText(receiptData.getFormattedAmount(), rightX, yPos, paint)
            yPos += LINE_HEIGHT + 8

            // Status (Centered, with background effect)
            paint.textSize = NORMAL_SIZE
            paint.typeface = Typeface.DEFAULT_BOLD
            paint.textAlign = Paint.Align.CENTER

            // Draw status with green color for APPROVED
            if (receiptData.paymentStatus.equals("APPROVED", ignoreCase = true)) {
                paint.color = Color.parseColor("#2E7D32") // Dark green
            } else {
                paint.color = Color.BLACK
            }

            canvas.drawText("*** ${receiptData.paymentStatus} ***", centerX, yPos, paint)

            // Reset color to black
            paint.color = Color.BLACK
            yPos += LINE_HEIGHT

            return yPos
        }

        private fun drawVatTipSubtotal(canvas: Canvas, paint: Paint, receiptData: ReceiptData, startY: Float): Float {
            var yPos = startY
            val leftX = RECEIPT_X_OFFSET + MARGIN
            val rightX = RECEIPT_X_OFFSET + RECEIPT_WIDTH - MARGIN

            paint.textSize = NORMAL_SIZE
            paint.typeface = Typeface.DEFAULT

            // Calculate subtotal (amount before VAT and tip)
            val baseAmount = receiptData.amount.toDoubleOrNull() ?: 0.0
            val vatAmount = receiptData.vatAmount?.toDoubleOrNull() ?: 0.0
            val tipAmount = receiptData.tipAmount?.toDoubleOrNull() ?: 0.0
            val subtotal = baseAmount - vatAmount - tipAmount

            // VAT - only show if amount is not null, not empty, and not "0" or "0.00"
            if (!receiptData.vatAmount.isNullOrEmpty() &&
                receiptData.vatAmount != "0" &&
                receiptData.vatAmount != "0.00" &&
                !receiptData.vatPercentage.isNullOrEmpty()) {
                paint.textAlign = Paint.Align.LEFT
                canvas.drawText("VAT (${receiptData.vatPercentage}%):", leftX, yPos, paint)
                paint.textAlign = Paint.Align.RIGHT
                canvas.drawText(receiptData.getFormattedVatAmount() ?: "", rightX, yPos, paint)
                yPos += SMALL_LINE_HEIGHT
            }

            // Tip - only show if amount is not null, not empty, and not "0" or "0.00"
            if (!receiptData.tipAmount.isNullOrEmpty() &&
                receiptData.tipAmount != "0" &&
                receiptData.tipAmount != "0.00" &&
                !receiptData.tipPercentage.isNullOrEmpty()) {
                paint.textAlign = Paint.Align.LEFT
                canvas.drawText("Tip (${receiptData.tipPercentage}%):", leftX, yPos, paint)
                paint.textAlign = Paint.Align.RIGHT
                canvas.drawText(receiptData.getFormattedTipAmount() ?: "", rightX, yPos, paint)
                yPos += SMALL_LINE_HEIGHT
            }

            // Subtotal (always show)
            paint.textAlign = Paint.Align.LEFT
            canvas.drawText("Subtotal:", leftX, yPos, paint)
            paint.textAlign = Paint.Align.RIGHT
            canvas.drawText("${receiptData.currency} ${String.format("%.2f", subtotal)}", rightX, yPos, paint)
            yPos += SMALL_LINE_HEIGHT

            return yPos
        }

        private fun drawTransactionDetails(canvas: Canvas, paint: Paint, receiptData: ReceiptData, startY: Float): Float {
            var yPos = startY
            val leftX = RECEIPT_X_OFFSET + MARGIN
            val rightX = RECEIPT_X_OFFSET + RECEIPT_WIDTH - MARGIN

            paint.textSize = SMALL_SIZE
            paint.typeface = Typeface.DEFAULT

            // Transaction details in proper order (Date/Time and Receipt # are now above)
            val details = mutableListOf<Pair<String, String>>()

            // Terminal ID (TID)
            if (!receiptData.terminalId.isNullOrEmpty()) {
                details.add("TID" to receiptData.terminalId)
            }

            // Merchant ID (MID) - using merchant name as placeholder
            details.add("MID" to (receiptData.merchantName.take(15)))

            // Payment Method
            details.add("Payment Method" to receiptData.paymentMethod)

            // Card Number
            details.add("Card Number" to receiptData.cardMask)

            // Transaction Type
            details.add("Transaction Type" to receiptData.transactionType)

            // RRN
            details.add("RRN" to receiptData.rrn)

            // Auth Code
            details.add("Auth Code" to receiptData.authCode)

            // AID
            details.add("AID" to receiptData.aid)

            // TVR if available
            if (!receiptData.tvr.isNullOrEmpty()) {
                details.add("TVR" to receiptData.tvr)
            }

            details.forEach { (label, value) ->
                paint.textAlign = Paint.Align.LEFT
                canvas.drawText("$label:", leftX, yPos, paint)
                paint.textAlign = Paint.Align.RIGHT
                canvas.drawText(value, rightX, yPos, paint)
                yPos += SMALL_LINE_HEIGHT
            }

            return yPos
        }

        private fun drawFooter(canvas: Canvas, paint: Paint, yPos: Float) {
            val centerX = RECEIPT_X_OFFSET + (RECEIPT_WIDTH / 2f)

            paint.textSize = SMALL_SIZE
            paint.typeface = Typeface.DEFAULT
            paint.textAlign = Paint.Align.CENTER

            canvas.drawText("Thank you for your business!", centerX, yPos + LINE_HEIGHT, paint)
            canvas.drawText("Keep this receipt for your records", centerX, yPos + LINE_HEIGHT * 2, paint)

            // Add HazelsOne branding at the bottom
            paint.textSize = SMALL_SIZE - 2f
            paint.typeface = Typeface.DEFAULT
            canvas.drawText("Made by HazelsOne a product of EftaaPay", centerX, yPos + LINE_HEIGHT * 3.5f, paint)
        }

        private fun showPrintDialog(context: Context, pdfDocument: PdfDocument, receiptNumber: String) {
            val printManager = context.getSystemService(Context.PRINT_SERVICE) as PrintManager
            val printAdapter = PdfDocumentAdapter(pdfDocument)

            printManager.print("Receipt_$receiptNumber", printAdapter, PrintAttributes.Builder().build())
        }

        /**
         * Create a sample receipt for testing purposes
         */
        fun createSampleReceipt(context: Context): ReceiptData {
            return ReceiptData(
                merchantName = "Sample Merchant",
                companyName = "Sample Company Ltd",
                merchantAddress = "123 Main Street",
                merchantCity = "Sample City",
                merchantCountry = "Sample Country",
                terminalId = "T001",
                receiptNumber = "RCP${System.currentTimeMillis()}",
                amount = "25.50",
                currency = "USD",
                dateTime = "2024-01-15 14:30:25",
                paymentStatus = "APPROVED",
                paymentMethod = "DEBIT MASTERCARD Contactless",
                cardMask = "****1234",
                transactionType = "PURCHASE",
                tvr = "0000008000",
                rrn = "123456789012",
                authCode = "123456",
                aid = "A0000000041010",
                vatAmount = "2.55",
                vatPercentage = "10",
                tipAmount = "3.83",
                tipPercentage = "15"
            )
        }
    }

    private class PdfDocumentAdapter(private val pdfDocument: PdfDocument) : PrintDocumentAdapter() {

        override fun onLayout(
            oldAttributes: PrintAttributes?,
            newAttributes: PrintAttributes,
            cancellationSignal: android.os.CancellationSignal?,
            callback: LayoutResultCallback,
            extras: android.os.Bundle?
        ) {
            if (cancellationSignal?.isCanceled == true) {
                callback.onLayoutCancelled()
                return
            }

            val info = PrintDocumentInfo.Builder("receipt.pdf")
                .setContentType(PrintDocumentInfo.CONTENT_TYPE_DOCUMENT)
                .setPageCount(1)
                .build()

            callback.onLayoutFinished(info, true)
        }

        override fun onWrite(
            pages: Array<out android.print.PageRange>,
            destination: android.os.ParcelFileDescriptor,
            cancellationSignal: android.os.CancellationSignal?,
            callback: WriteResultCallback
        ) {
            try {
                if (cancellationSignal?.isCanceled == true) {
                    callback.onWriteCancelled()
                    return
                }

                FileOutputStream(destination.fileDescriptor).use { outputStream ->
                    pdfDocument.writeTo(outputStream)
                }

                callback.onWriteFinished(arrayOf(android.print.PageRange.ALL_PAGES))

            } catch (e: Exception) {
                callback.onWriteFailed(e.message)
            } finally {
                pdfDocument.close()
            }
        }
    }
}
